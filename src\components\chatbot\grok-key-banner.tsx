import React from "react";
import { Link as RouterLink } from "@tanstack/react-router";
import { ArrowRight } from "react-feather";

interface GrokKeyBannerProps {
	hasGrokKey: boolean;
	className?: string;
}

const GrokKeyBanner: React.FC<GrokKeyBannerProps> = ({ hasGrokKey, className = "" }) => {
	if (hasGrok<PERSON>ey) {
		return (
			<div className={`bg-gradient-to-r from-purple-500 to-purple-600 rounded-xl p-4 text-white ${className}`}>
				<div className="flex items-center justify-between">
					<div className="flex items-center gap-3">
						<div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
							<span className="text-2xl">🤖</span>
						</div>
						<div>
							<h3 className="font-semibold text-lg">You have Unlimited</h3>
							<p className="text-purple-100 text-sm">Private Chatbot!</p>
						</div>
					</div>
					<RouterLink 
						to="/chatbot/manage-key"
						className="flex items-center gap-2 bg-white/20 hover:bg-white/30 px-4 py-2 rounded-lg transition-colors"
					>
						<span className="text-sm font-medium">Change Grok Key</span>
						<ArrowRight size={16} />
					</RouterLink>
				</div>
			</div>
		);
	}

	return (
		<div className={`bg-gradient-to-r from-purple-100 to-purple-200 rounded-xl p-4 border border-purple-300 ${className}`}>
			<div className="flex items-center justify-between">
				<div className="flex items-center gap-3">
					<div className="w-12 h-12 bg-purple-500/20 rounded-full flex items-center justify-center">
						<span className="text-2xl">🤖</span>
					</div>
					<div>
						<h3 className="font-semibold text-lg text-purple-800">Want unlimited Chatbot?</h3>
						<p className="text-purple-600 text-sm">Bring your own key!</p>
					</div>
				</div>
				<RouterLink 
					to="/chatbot/add-key"
					className="flex items-center gap-2 bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded-lg transition-colors"
				>
					<span className="text-sm font-medium">Add Grok Key</span>
					<ArrowRight size={16} />
				</RouterLink>
			</div>
		</div>
	);
};

export default GrokKeyBanner;
