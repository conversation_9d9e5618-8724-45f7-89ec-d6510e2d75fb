import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { checkGrokKeyStatus, addGrok<PERSON>ey } from "@/features/chatbot/services";
import type { AddGrokKeyRequest } from "@/features/chatbot/types";

export const useGrokKeyStatus = () => {
	return useQuery({
		queryKey: ["grokKeyStatus"],
		queryFn: checkGrokKeyStatus,
		select: (data) => data.data,
		retry: 1,
		refetchOnWindowFocus: false,
	});
};

export const useAddGrokKey = () => {
	const queryClient = useQueryClient();
	
	return useMutation({
		mutationFn: (data: AddGrokKeyRequest) => addGrokKey(data),
		onSuccess: () => {
			// Invalidate and refetch the Grok key status
			queryClient.invalidateQueries({ queryKey: ["grokKeyStatus"] });
		},
	});
};
